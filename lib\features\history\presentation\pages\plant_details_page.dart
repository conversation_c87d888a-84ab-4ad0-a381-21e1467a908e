import 'package:flutter/material.dart';
import '../../../../../../models/plant_model.dart';
import '../../../../core/utils/constants.dart';

class PlantDetailsPage extends StatelessWidget {
  final PlantModel plant;
  const PlantDetailsPage({super.key, required this.plant});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Plant Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: Image.network(
                plant.imagePath,
                width: 220,
                height: 220,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) =>
                    const Icon(Icons.image, size: 120),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              plant.type,
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(
              'Scanned: ${plant.timestamp.toLocal().toString().split(".").first.replaceFirst("T", " ")}',
              style: const TextStyle(fontSize: 15, color: Colors.black54),
            ),
            const SizedBox(height: 32),
            const Divider(),
            const SizedBox(height: 16),
            const Text(
              'Care Info (coming soon)',
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: AppColors.purple),
            ),
            const SizedBox(height: 8),
            const Text(
              'You will see personalized care tips for this plant here in a future update.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 15, color: Colors.black54),
            ),
          ],
        ),
      ),
    );
  }
}
