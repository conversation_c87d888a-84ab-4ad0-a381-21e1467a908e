import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/utils/constants.dart';
import '../../../../core/widgets/app_button.dart';
import '../../../../../../models/plant_model.dart';
import '../../../../../../services/local_storage_service.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  String? _selectedPlantType;
  XFile? _selectedImage;
  bool _isIdentifying = false;

  final ImagePicker _picker = ImagePicker();
  final List<String> _plantTypes = [
    'Flower',
    'Tree',
    'Shrub',
    'Succulent',
    'Herb',
    'Vine',
  ];

  Future<void> _handleGallery() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() => _selectedImage = image);
    }
  }

  Future<void> _handleCamera() async {
    final XFile? image = await _picker.pickImage(source: ImageSource.camera);
    if (image != null) {
      setState(() => _selectedImage = image);
    }
  }

  Future<void> _handleIdentify() async {
    if (_selectedImage == null || _selectedPlantType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select both image and plant type.')),
      );
      return;
    }
    setState(() => _isIdentifying = true);
    await Future.delayed(const Duration(seconds: 1));
    final plant = PlantModel(
      imagePath: _selectedImage!.path,
      type: _selectedPlantType!,
      timestamp: DateTime.now(),
    );
    await LocalStorageService().addPlantToHistory(plant);
    setState(() {
      _isIdentifying = false;
      _selectedImage = null;
      _selectedPlantType = null;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Plant identified and saved to history!')),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: const Text('AI Plant Identifier',
            style:
                TextStyle(color: AppColors.green, fontWeight: FontWeight.bold)),
        actions: [
          IconButton(
            icon: Icon(Icons.chat, color: AppColors.green),
            onPressed: () {},
            tooltip: 'Contact',
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppPadding.horizontal),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              const Text('Identify Your Plant',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22)),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  prefixIcon: const Icon(Icons.local_florist),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12)),
                  contentPadding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                ),
                hint: const Text('Select Plant Type'),
                value: _selectedPlantType,
                items: _plantTypes
                    .map((type) =>
                        DropdownMenuItem(value: type, child: Text(type)))
                    .toList(),
                onChanged: (val) => setState(() => _selectedPlantType = val),
              ),
              const SizedBox(height: 16),
              Card(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                elevation: 2,
                child: SizedBox(
                  width: double.infinity,
                  height: 220,
                  child: _selectedImage == null
                      ? Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: const [
                            Icon(Icons.image_search,
                                size: 48, color: Colors.grey),
                            SizedBox(height: 8),
                            Text('Select an image to identify',
                                style: TextStyle(color: Colors.black54)),
                          ],
                        )
                      : ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: Image.network(_selectedImage!.path,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity),
                        ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _handleGallery,
                      icon: const Icon(Icons.image, color: AppColors.green),
                      label: const Text('Gallery',
                          style: TextStyle(color: AppColors.green)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.lightGreen.withOpacity(0.2),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16)),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _handleCamera,
                      icon:
                          const Icon(Icons.camera_alt, color: AppColors.green),
                      label: const Text('Camera',
                          style: TextStyle(color: AppColors.green)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.lightGreen.withOpacity(0.2),
                        elevation: 0,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16)),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              AppButton(
                text: _isIdentifying ? 'Identifying...' : 'Identify Plant',
                onPressed: (_selectedImage != null &&
                        _selectedPlantType != null &&
                        !_isIdentifying)
                    ? _handleIdentify
                    : null,
                enabled: _selectedImage != null &&
                    _selectedPlantType != null &&
                    !_isIdentifying,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
