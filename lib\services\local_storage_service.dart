import 'package:hive/hive.dart';
import '../models/plant_model.dart';

class LocalStorageService {
  static final LocalStorageService _instance = LocalStorageService._internal();
  factory LocalStorageService() => _instance;
  LocalStorageService._internal();

  Future<void> addPlantToHistory(PlantModel plant) async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.add(plant);
  }

  List<PlantModel> getAllPlants() {
    final box = Hive.box<PlantModel>('plantsBox');
    return box.values.toList();
  }

  Future<void> clearHistory() async {
    final box = Hive.box<PlantModel>('plantsBox');
    await box.clear();
  }
}
