import 'package:flutter/material.dart';
import 'home/presentation/pages/home_page.dart';
import 'history/presentation/pages/history_page.dart';

class AppNavigator extends StatefulWidget {
  const AppNavigator({super.key});

  @override
  State<AppNavigator> createState() => _AppNavigatorState();
}

class _AppNavigatorState extends State<AppNavigator> {
  int _currentIndex = 0;
  final List<Widget> _screens = const [
    HomePage(),
    HistoryPage(),
    _ProgressPage(),
    _InsightsPage(),
    _RoutinePage(),
    _DiaryPage(),
    _AICoachPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _currentIndex,
        selectedItemColor: const Color(0xFF7C4DFF),
        unselectedItemColor: Colors.grey,
        onTap: (index) => setState(() => _currentIndex = index),
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.camera_alt), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.history), label: 'History'),
          BottomNavigationBarItem(
              icon: Icon(Icons.show_chart), label: 'Progress'),
          BottomNavigationBarItem(
              icon: Icon(Icons.insights), label: 'Insights'),
          BottomNavigationBarItem(
              icon: Icon(Icons.event_note), label: 'Routine'),
          BottomNavigationBarItem(icon: Icon(Icons.book), label: 'Diary'),
          BottomNavigationBarItem(
              icon: Icon(Icons.chat_bubble), label: 'AI Coach'),
        ],
      ),
    );
  }
}

class _StubScreen extends StatelessWidget {
  final String title;
  const _StubScreen({required this.title});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: Center(child: Text('$title Screen')),
    );
  }
}

class _ProgressPage extends StatelessWidget {
  const _ProgressPage();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Progress')),
      body: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: const [
            Icon(Icons.show_chart, size: 80, color: Color(0xFF7C4DFF)),
            SizedBox(height: 24),
            Text('Track Your Progress',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('See your plant identification journey and milestones here.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.black54)),
            SizedBox(height: 32),
            Text('Progress analytics coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.black38)),
          ],
        ),
      ),
    );
  }
}

class _InsightsPage extends StatelessWidget {
  const _InsightsPage();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Insights')),
      body: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: const [
            Icon(Icons.insights, size: 80, color: Color(0xFF7C4DFF)),
            SizedBox(height: 24),
            Text('Plant Insights',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('Get personalized insights about your plant collection.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.black54)),
            SizedBox(height: 32),
            Text('Insights features coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.black38)),
          ],
        ),
      ),
    );
  }
}

class _RoutinePage extends StatelessWidget {
  const _RoutinePage();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Routine')),
      body: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: const [
            Icon(Icons.event_note, size: 80, color: Color(0xFF7C4DFF)),
            SizedBox(height: 24),
            Text('Care Routine',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('Manage and personalize your plant care routines.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.black54)),
            SizedBox(height: 32),
            Text('Routine planner coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.black38)),
          ],
        ),
      ),
    );
  }
}

class _DiaryPage extends StatelessWidget {
  const _DiaryPage();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Diary')),
      body: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: const [
            Icon(Icons.book, size: 80, color: Color(0xFF7C4DFF)),
            SizedBox(height: 24),
            Text('Plant Diary',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('Record your plant observations and notes.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.black54)),
            SizedBox(height: 32),
            Text('Diary feature coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.black38)),
          ],
        ),
      ),
    );
  }
}

class _AICoachPage extends StatelessWidget {
  const _AICoachPage();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('AI Coach')),
      body: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: const [
            Icon(Icons.chat_bubble, size: 80, color: Color(0xFF7C4DFF)),
            SizedBox(height: 24),
            Text('AI Plant Coach',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
            SizedBox(height: 12),
            Text('Chat with your AI assistant for plant care advice.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.black54)),
            SizedBox(height: 32),
            Text('AI Coach coming soon!',
                style: TextStyle(fontSize: 16, color: Colors.black38)),
          ],
        ),
      ),
    );
  }
}
