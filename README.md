# AI Plant Identifier

A Flutter app for AI-based plant identification, following feature-first clean architecture and modern UI/UX best practices.

## Features
- Onboarding screens (with images)
- Plant scanning/photo capture
- History of scanned plants
- Plant details display
- Plant health insights
- AI personalized plant care routines
- Plant diary
- Progress tracking
- AI plant assistant chatbot

## Architecture
- Feature-first, Clean Architecture
- Uses Hive for local storage, SharedPreferences for flags
- Bloc/Cubit for state management (future phases)

## Setup
1. Ensure Flutter SDK is installed (>=3.0.0)
2. Run `flutter pub get`
3. Place UI images in `assets/images/` (already referenced in pubspec.yaml)
4. Run on device or web: `flutter run -d chrome`

## Assets
All UI images are stored in `assets/images/` and used in onboarding and home screens.

## Build & Error Prevention
- Follows best practices for Gradle, dependencies, and permissions (see `build_instructions.txt`)
- Null safety and validation throughout

## License
MIT 