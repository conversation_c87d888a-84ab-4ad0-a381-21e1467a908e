import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'models/plant_model.dart';
import 'features/onboarding/presentation/pages/onboarding_page.dart';
import 'features/app_navigator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  Hive.registerAdapter(PlantModelAdapter());
  await Hive.openBox<PlantModel>('plantsBox');
  final prefs = await SharedPreferences.getInstance();
  final showOnboarding = prefs.getBool('hasSeenOnboarding') ?? true;
  runApp(MyApp(showOnboarding: showOnboarding));
}

class MyApp extends StatefulWidget {
  final bool showOnboarding;
  const MyApp({super.key, required this.showOnboarding});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late bool _showOnboarding;

  @override
  void initState() {
    super.initState();
    _showOnboarding = widget.showOnboarding;
  }

  Future<void> _completeOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('hasSeenOnboarding', false);
    setState(() => _showOnboarding = false);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AI Plant Identifier',
      theme: ThemeData(
        primaryColor: const Color(0xFF388E3C),
        scaffoldBackgroundColor: Color(0xFFFAF7FB),
        useMaterial3: true,
      ),
      home: _showOnboarding
          ? OnboardingPage(onComplete: _completeOnboarding)
          : const AppNavigator(),
      debugShowCheckedModeBanner: false,
    );
  }
}
