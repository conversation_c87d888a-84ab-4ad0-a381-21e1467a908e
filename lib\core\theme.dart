import 'package:flutter/material.dart';

final ThemeData appTheme = ThemeData(
  primaryColor: const Color(0xFF388E3C), // Deep green
  colorScheme: ColorScheme.fromSeed(
    seedColor: const Color(0xFF388E3C),
    primary: const Color(0xFF388E3C),
    secondary: const Color(0xFF7C4DFF), // Purple accent
    background: const Color(0xFFFAF7FB),
  ),
  scaffoldBackgroundColor: const Color(0xFFFAF7FB),
  textTheme: const TextTheme(
    headlineLarge: TextStyle(
        fontSize: 26, fontWeight: FontWeight.bold, color: Colors.black),
    bodyMedium: TextStyle(fontSize: 16, color: Colors.black54),
    labelLarge: TextStyle(
        fontSize: 18, fontWeight: FontWeight.bold, color: Colors.white),
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: const Color(0xFF388E3C),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
      textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    ),
  ),
  useMaterial3: true,
);
