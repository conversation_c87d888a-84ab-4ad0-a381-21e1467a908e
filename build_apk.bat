@echo off
echo ========================================
echo AI Plant Identifier - APK Builder
echo ========================================
echo.

echo Step 1: Stopping any running Gradle daemons...
call flutter clean
call gradle --stop 2>nul

echo.
echo Step 2: Clearing Gradle cache...
if exist "%USERPROFILE%\.gradle\wrapper\dists\gradle-8.14-all" (
    echo Removing old Gradle 8.14 cache...
    rmdir /s /q "%USERPROFILE%\.gradle\wrapper\dists\gradle-8.14-all" 2>nul
)

if exist "%USERPROFILE%\.gradle\wrapper\dists\gradle-8.10.2-all" (
    echo Removing old Gradle 8.10.2 cache...
    rmdir /s /q "%USERPROFILE%\.gradle\wrapper\dists\gradle-8.10.2-all" 2>nul
)

echo.
echo Step 3: Getting Flutter dependencies...
call flutter pub get

echo.
echo Step 4: Building APK (Debug - faster build)...
call flutter build apk --debug --verbose

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo SUCCESS! APK built successfully!
    echo ========================================
    echo.
    echo Your APK is located at:
    echo build\app\outputs\flutter-apk\app-debug.apk
    echo.
    echo File size:
    if exist "build\app\outputs\flutter-apk\app-debug.apk" (
        for %%A in ("build\app\outputs\flutter-apk\app-debug.apk") do echo %%~zA bytes
    )
    echo.
    echo To install on Android device:
    echo 1. Enable 'Unknown Sources' in Android settings
    echo 2. Transfer the APK to your device
    echo 3. Tap the APK file to install
    echo.
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Trying alternative build method...
    echo.
    echo Building Release APK...
    call flutter build apk --release --no-tree-shake-icons
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo SUCCESS! Release APK built!
        echo Location: build\app\outputs\flutter-apk\app-release.apk
    ) else (
        echo.
        echo Both builds failed. Please check the error messages above.
        echo.
        echo Manual steps to try:
        echo 1. Restart your computer
        echo 2. Run: flutter doctor
        echo 3. Run: flutter clean
        echo 4. Run: flutter pub get
        echo 5. Try building again
    )
)

echo.
pause
