import 'package:flutter/material.dart';
import '../../../../core/utils/constants.dart';
import '../../../../core/widgets/app_button.dart';

class OnboardingContent extends StatelessWidget {
  final String imageAsset, title, description;
  final bool isLastPage;
  final VoidCallback onContinue, onSkip;

  const OnboardingContent({
    super.key,
    required this.imageAsset,
    required this.title,
    required this.description,
    required this.isLastPage,
    required this.onContinue,
    required this.onSkip,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppPadding.horizontal),
      child: Column(
        children: [
          Align(
            alignment: Alignment.topRight,
            child: TextButton(
              onPressed: onSkip,
              child: const Text('Skip',
                  style: TextStyle(
                      color: AppColors.lightGreen,
                      fontWeight: FontWeight.w500,
                      fontSize: 16)),
            ),
          ),
          const Spacer(),
          Container(
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 16)],
            ),
            child: ClipOval(
              child: Image.asset(imageAsset,
                  width: 220,
                  height: 220,
                  fit: BoxFit.cover,
                  semanticLabel: title),
            ),
          ),
          const Spacer(),
          Text(title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.headlineLarge),
          const SizedBox(height: 16),
          Text(description,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium),
          const Spacer(),
          // Dots indicator will be handled by parent
          const SizedBox(height: 32),
          AppButton(
            text: isLastPage ? 'Get Started' : 'Continue',
            onPressed: onContinue,
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }
}
