import 'package:flutter/material.dart';
import '../../../../core/utils/constants.dart';
import '../widgets/onboarding_content.dart';

class OnboardingPage extends StatefulWidget {
  final VoidCallback onComplete;
  const OnboardingPage({super.key, required this.onComplete});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _controller = PageController();
  int _currentIndex = 0;

  final List<Map<String, String>> _pages = [
    {
      'image': AppAssets.onboarding1,
      'title': "Explore Nature's Diversity",
      'desc':
          "Build your personal plant collection, learn about different species, and deepen your connection with the natural world.",
    },
    {
      'image': AppAssets.onboarding2,
      'title': "Discover Plant Details & Care",
      'desc':
          "Get rich information, including common & scientific names, characteristics, and essential care tips for your identified plants.",
    },
    {
      'image': AppAssets.onboarding3,
      'title': "Snap a Plant, Unveil Its Name",
      'desc':
          "Use your phone's camera to instantly identify flowers, trees, and other plants around you. It's quick and easy!",
    },
  ];

  void _handleContinue() {
    if (_currentIndex == _pages.length - 1) {
      widget.onComplete();
    } else {
      _controller.nextPage(
          duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

  void _handleSkip() {
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _controller,
                itemCount: _pages.length,
                onPageChanged: (index) => setState(() => _currentIndex = index),
                itemBuilder: (context, index) {
                  final page = _pages[index];
                  return OnboardingContent(
                    imageAsset: page['image']!,
                    title: page['title']!,
                    description: page['desc']!,
                    isLastPage: index == _pages.length - 1,
                    onContinue: _handleContinue,
                    onSkip: _handleSkip,
                  );
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_pages.length, (index) {
                final isActive = _currentIndex == index;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: isActive ? 16 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: isActive ? AppColors.purple : Colors.grey.shade400,
                    borderRadius: BorderRadius.circular(4),
                  ),
                );
              }),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
